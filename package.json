{"name": "claude-code-ui", "version": "1.2.0", "description": "A web-based UI for Claude Code CLI", "type": "module", "main": "server/index.js", "scripts": {"dev": "concurrently --kill-others \"npm run server\" \"npm run client\"", "server": "node server/index.js", "client": "vite --host", "build": "vite build", "preview": "vite preview", "start": "npm run build && npm run server"}, "keywords": ["claude", "ai", "code", "ui", "assistant"], "author": "Claude Code UI Contributors", "license": "MIT", "dependencies": {"@anthropic-ai/claude-code": "^1.0.24", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.3", "@codemirror/lang-python": "^6.2.1", "@codemirror/theme-one-dark": "^6.1.2", "@tailwindcss/typography": "^0.5.16", "@uiw/react-codemirror": "^4.23.13", "@xterm/addon-clipboard": "^0.1.0", "@xterm/addon-webgl": "^0.18.0", "bcrypt": "^6.0.0", "better-sqlite3": "^12.2.0", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.515.0", "mime-types": "^3.0.1", "node-fetch": "^2.7.0", "node-pty": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.8.1", "tailwind-merge": "^3.3.1", "ws": "^8.14.2", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "postcss": "^8.4.32", "sharp": "^0.34.2", "tailwindcss": "^3.4.0", "vite": "^7.0.4"}}