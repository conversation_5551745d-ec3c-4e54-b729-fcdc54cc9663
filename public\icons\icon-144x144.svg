<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background fills entire canvas - iOS will handle corner rounding -->
  <rect width="512" height="512" fill="hsl(240 5.9% 10%)"/>
  
  <!-- MessageSquare icon - scaled and centered -->
  <path d="M128 144C128 126.327 142.327 112 160 112H352C369.673 112 384 126.327 384 144V272C384 289.673 369.673 304 352 304H224L128 400V144Z" 
        stroke="white" 
        stroke-width="32" 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        fill="none"/>
</svg>